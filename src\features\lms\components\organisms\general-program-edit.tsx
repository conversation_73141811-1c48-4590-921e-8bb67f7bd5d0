import { Form, Input, Select, Tag, DatePicker, Button } from "antd";
import dayjs from "dayjs";

const { Option } = Select;
const { RangePicker } = DatePicker;

import {
    OfferingFormat,
    OfferingFormatLabel,
    OfferingModality,
    OfferingModalityLabel,
    OfferingStage,
    OfferingStageLabel,
    OfferingType,
    OfferingTypeColor,
    OfferingTypeLabel,
    PartialUpdateOfferingBody,
} from "@myTypes/offering";
import Save from "@assets/icons/general/save-stroke.svg?react";
import FormLabel from "@/features/crm/components/atoms/FormLabel";
import { type PartialUpdateProgramBody, Program } from "../../types/program";
import { ArrowLeft } from "lucide-react";
import { useUpdateProgram } from "../../hooks/use-program";

type GeneralOfferingEditProps = {
    data: Program;
    oid: string;
    onUpdateSuccess?: () => void;
    onCancel?: () => void;
};

const GeneralProgramEdit: React.FC<GeneralOfferingEditProps> = ({
    data,
    oid,
    onUpdateSuccess,
    onCancel,
}) => {
    // const navigate = useNavigate();
    const { mutate: updateProgram, isPending } = useUpdateProgram({
        onSuccess: () => {
            onUpdateSuccess?.();
        },
    });
    const [form] = Form.useForm<PartialUpdateProgramBody>();

    const handleFormFinish = (values: PartialUpdateProgramBody) => {
        const payload = {
            ...values,
            startDate: values.rangeDate![0].format("YYYY-MM-DD"),
            endDate: values.rangeDate![1].format("YYYY-MM-DD"),
        };
        updateProgram({ oid, data: payload });
    };

    const handleSave = () => {
        form.submit();
    };

    const handleCancel = () => {
        onCancel?.();
    };

    return (
        <Form
            name="offeringEdit"
            layout="vertical"
            form={form}
            initialValues={{
                ...data,
                rangeDate: [dayjs(data?.startDate), dayjs(data?.endDate)],
            }}
            onFinish={handleFormFinish}
        >
            <div className="grid grid-cols-1 lg:grid-cols-6 gap-y-6 lg:gap-6">
                <div className="bg-white-full col-span-4 p-5">
                    <p className="text-gray-400 font-semibold text-sm">
                        INFORMACIÓN GENERAL
                    </p>
                    <Form.Item<PartialUpdateOfferingBody>
                        name="name"
                        label={
                            <FormLabel className="font-semibold text-base">
                                Nombre del Producto
                            </FormLabel>
                        }
                        rules={[
                            {
                                required: true,
                                message: "Por favor, ingrese el nombre del Producto",
                            },
                        ]}
                    >
                        <Input
                            placeholder="Ej. Curso de Marketing Digital"
                            className="py-2"
                        />
                    </Form.Item>

                    <Form.Item<PartialUpdateOfferingBody>
                        name="longName"
                        label={
                            <FormLabel className="font-semibold text-base">
                                Nombre largo del producto
                            </FormLabel>
                        }
                    >
                        <Input
                            placeholder="Ej. I Curso Completo de Marketing Digital y Estrategias de Ventas 2025"
                            className="py-2"
                        />
                    </Form.Item>

                    <Form.Item<PartialUpdateOfferingBody>
                        name="description"
                        label={
                            <FormLabel className="font-semibold text-base">
                                Descripción del producto
                            </FormLabel>
                        }
                    >
                        <Input.TextArea
                            placeholder="Descripción del producto"
                            className="py-2"
                            autoSize={{
                                minRows: 3,
                                maxRows: 7,
                            }}
                        />
                    </Form.Item>

                    <div className="grid grid-cols-2 gap-4">
                        <Form.Item<PartialUpdateOfferingBody>
                            name="duration"
                            label={
                                <FormLabel className="font-semibold text-base">
                                    Duración
                                </FormLabel>
                            }
                        >
                            <Input
                                placeholder="Ej. 6 semanas, 3 meses"
                                className="py-2"
                            />
                        </Form.Item>
                        <Form.Item<PartialUpdateOfferingBody>
                            name="hours"
                            label={
                                <FormLabel className="font-semibold text-base">
                                    Horas
                                </FormLabel>
                            }
                        >
                            <Input
                                type="number"
                                placeholder="Ej. 40"
                                className="py-2"
                            />
                        </Form.Item>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <Form.Item<PartialUpdateOfferingBody>
                            name="type"
                            label={
                                <FormLabel className="font-semibold text-base">
                                    Tipo
                                </FormLabel>
                            }
                            rules={[
                                {
                                    required: true,
                                    message:
                                        "Por favor, seleccione el tipo de producto.",
                                },
                            ]}
                        >
                            <Select>
                                {Object.values(OfferingType).map((type) => (
                                    <Option key={type} value={type}>
                                        <Tag
                                            color={OfferingTypeColor[type]}
                                            className="rounded-full px-3"
                                            bordered={false}
                                        >
                                            {OfferingTypeLabel[type]}
                                        </Tag>
                                    </Option>
                                ))}
                            </Select>
                        </Form.Item>
                        <Form.Item<PartialUpdateOfferingBody>
                            name="format"
                            label={
                                <FormLabel className="font-semibold text-base">
                                    Formato
                                </FormLabel>
                            }
                            rules={[
                                {
                                    required: true,
                                    message: "Por favor, el formato del producto",
                                },
                            ]}
                        >
                            <Select>
                                <Option value={OfferingFormat.ASYNCHRONOUS}>
                                    <Tag
                                        color="green"
                                        className="rounded-full px-3"
                                        bordered={false}
                                    >
                                        {
                                            OfferingFormatLabel[
                                                OfferingFormat.ASYNCHRONOUS
                                            ]
                                        }
                                    </Tag>
                                </Option>
                                <Option value={OfferingFormat.LIVE}>
                                    <Tag
                                        color="blue"
                                        className="rounded-full px-3"
                                        bordered={false}
                                    >
                                        {OfferingFormatLabel[OfferingFormat.LIVE]}
                                    </Tag>
                                </Option>
                            </Select>
                        </Form.Item>
                        <Form.Item<PartialUpdateOfferingBody>
                            name="modality"
                            label={
                                <FormLabel className="font-semibold text-base">
                                    Modalidad
                                </FormLabel>
                            }
                            rules={[
                                {
                                    required: true,
                                    message: "Por favor, la modalidad del producto",
                                },
                            ]}
                        >
                            <Select>
                                <Option value={OfferingModality.IN_PERSON}>
                                    <Tag
                                        color="geekblue"
                                        className="rounded-full px-3"
                                        bordered={false}
                                    >
                                        {
                                            OfferingModalityLabel[
                                                OfferingModality.IN_PERSON
                                            ]
                                        }
                                    </Tag>
                                </Option>
                                <Option value={OfferingModality.REMOTE}>
                                    <Tag
                                        color="purple"
                                        className="rounded-full px-3"
                                        bordered={false}
                                    >
                                        {OfferingModalityLabel[OfferingModality.REMOTE]}
                                    </Tag>
                                </Option>
                            </Select>
                        </Form.Item>
                    </div>

                    <div className="grid grid-cols-3 gap-4">
                        <Form.Item<PartialUpdateOfferingBody>
                            name="frequency"
                            label={
                                <FormLabel className="font-semibold text-base">
                                    Frecuencia
                                </FormLabel>
                            }
                            rules={[
                                {
                                    required: true,
                                    message: "Por favor, ingrese la frecuencia",
                                },
                            ]}
                        >
                            <Input
                                placeholder="Ej. Sábados y Domingos"
                                className="py-2"
                            />
                        </Form.Item>
                        <Form.Item<PartialUpdateOfferingBody>
                            name="schedule"
                            label={
                                <FormLabel className="font-semibold text-base">
                                    Horario
                                </FormLabel>
                            }
                            rules={[
                                {
                                    required: true,
                                    message: "Por favor, ingrese el horario",
                                },
                            ]}
                        >
                            <Input
                                placeholder="Ej. 9:00 AM - 1:00 PM"
                                className="py-2"
                            />
                        </Form.Item>
                        <Form.Item<PartialUpdateOfferingBody>
                            name="rangeDate"
                            label={
                                <FormLabel className="font-semibold text-base">
                                    Inicio {"y"} Fin
                                </FormLabel>
                            }
                            rules={[
                                {
                                    required: true,
                                    message:
                                        "Por favor, ingrese las fechas de inicio y fin del programa",
                                },
                            ]}
                        >
                            <RangePicker className="py-2 w-full" />
                        </Form.Item>
                    </div>
                </div>
                <div className="col-span-2 space-y-6">
                    <div className="bg-white-full p-5 rounded-lg shadow-sm">
                        <p className="text-gray-400 font-semibold text-sm">ACCIONES</p>
                        <div className="flex justify-end gap-3">
                            <Button
                                type="default"
                                onClick={handleCancel}
                                icon={<ArrowLeft />}
                                size="large"
                                disabled={isPending}
                            >
                                Cancelar
                            </Button>
                            <Button
                                type="primary"
                                size="large"
                                style={{ fontSize: 16 }}
                                icon={<Save />}
                                onClick={handleSave}
                                disabled={isPending}
                            >
                                Guardar
                            </Button>
                        </div>
                    </div>
                    <div className="bg-white-full p-5 rounded-lg shadow-sm">
                        <p className="text-gray-400 font-semibold text-sm">
                            VISIBILIDAD DEL CONTENIDO
                        </p>
                        <Form.Item<PartialUpdateOfferingBody>
                            name="stage"
                            label={
                                <FormLabel className="font-semibold text-base">
                                    Estado/Fase del producto
                                </FormLabel>
                            }
                            rules={[
                                {
                                    required: true,
                                    message: "Por favor, seleccione un estado/fase",
                                },
                            ]}
                        >
                            <Select>
                                <Option value={OfferingStage.PLANNING}>
                                    <Tag
                                        color="orange"
                                        className="rounded-full px-3"
                                        bordered={false}
                                    >
                                        {OfferingStageLabel[OfferingStage.PLANNING]}
                                    </Tag>
                                </Option>
                                <Option value={OfferingStage.LAUNCHED}>
                                    <Tag
                                        color="green"
                                        className="rounded-full px-3"
                                        bordered={false}
                                    >
                                        {OfferingStageLabel[OfferingStage.LAUNCHED]}
                                    </Tag>
                                </Option>
                                <Option value={OfferingStage.ENROLLMENT}>
                                    <Tag
                                        color="blue"
                                        className="rounded-full px-3"
                                        bordered={false}
                                    >
                                        {OfferingStageLabel[OfferingStage.ENROLLMENT]}
                                    </Tag>
                                </Option>
                                <Option value={OfferingStage.ENROLLMENT_CLOSED}>
                                    <Tag
                                        color="purple"
                                        className="rounded-full px-3"
                                        bordered={false}
                                    >
                                        {
                                            OfferingStageLabel[
                                                OfferingStage.ENROLLMENT_CLOSED
                                            ]
                                        }
                                    </Tag>
                                </Option>
                                <Option value={OfferingStage.FINISHED}>
                                    <Tag
                                        color="red"
                                        className="rounded-full px-3"
                                        bordered={false}
                                    >
                                        {OfferingStageLabel[OfferingStage.FINISHED]}
                                    </Tag>
                                </Option>
                            </Select>
                        </Form.Item>
                        <div className="grid grid-cols-2 gap-2">
                            <Form.Item<PartialUpdateOfferingBody>
                                name="codeName"
                                label={
                                    <FormLabel className="font-semibold text-base">
                                        Código del Producto
                                    </FormLabel>
                                }
                            >
                                <Input placeholder="Ej. PPCEU-2023-I" />
                            </Form.Item>
                        </div>
                    </div>
                </div>
            </div>
        </Form>
    );
};

export default GeneralProgramEdit;
