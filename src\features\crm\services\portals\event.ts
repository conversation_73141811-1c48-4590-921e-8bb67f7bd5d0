import { portalsApi } from "@services/portals";
import { Event, CreateEventFormBody, ListEventQueryParams } from "../../types/event";
import { UploadFile } from "antd";
import { PaginatedResponse } from "@myTypes/base";

export const listEvents = async (
    queryParams: ListEventQueryParams = {},
): Promise<PaginatedResponse<Event>> => {
    const res = await portalsApi.get("crm/events", { params: queryParams });
    return res.data;
};

export const createEvent = async (newEvent: CreateEventFormBody) => {
    const res = await portalsApi.post("crm/events", newEvent);
    return res.data;
};

export const getEvent = async (id: string): Promise<Event> => {
    const { data } = await portalsApi.get(`crm/events/${id}`);
    return data;
};

export const updateEvent = async (
    eid: string,
    data: Partial<CreateEventFormBody>,
): Promise<Event> => {
    const response = await portalsApi.patch(`crm/events/${eid}`, data);
    return response.data;
};

export const uploadEventCoverImage = async (eid: string, file: UploadFile) => {
    const formData = new FormData();
    formData.append("file", file as unknown as Blob);
    const response = await portalsApi.post(
        `crm/events/${eid}/upload-cover-image`,
        formData,
    );
    return response.data;
};
export const removeEventCoverImage = async (eid: string, fid: string) => {
    const response = await portalsApi.delete(
        `crm/events/${eid}/remove-cover-image/${fid}`,
    );
    return response.data;
};

export const uploadEventThumbnail = async (eid: string, file: UploadFile) => {
    const formData = new FormData();
    formData.append("file", file as unknown as Blob);
    const response = await portalsApi.post(
        `crm/events/${eid}/upload-thumbnail`,
        formData,
    );
    return response.data;
};

export const removeEventThumbnail = async (eid: string, fid: string) => {
    const response = await portalsApi.delete(
        `crm/events/${eid}/remove-thumbnail/${fid}`,
    );
    return response.data;
};
