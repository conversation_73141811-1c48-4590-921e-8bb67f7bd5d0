import {
    Event,
    EventModality,
    EventModalityLabels,
    EventType,
    EventTypeLabels,
    CreateEventFormBody,
} from "@/features/crm/types/event";
import { Button, Form, Input, Select } from "antd";
import { Save, Trash } from "lucide-react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { updateEvent } from "@/features/crm/services/portals/event";
import FormLabel from "@/features/crm/components/atoms/FormLabel";
import SelectInstructor from "../../molecules/select-instructor";
import UploadEventCover from "../../molecules/upload-event-cover";
import UploadEventThumbnail from "../../molecules/upload-event-thumbnail";
import SelectOfferings from "../../molecules/select-offerings";
import { App } from "antd";
import { onSuccessMessage } from "../../../../../lib/message";
const { TextArea } = Input;

type GeneralEventDetailProps = {
    event: Event;
};

export default function GeneralEventDetail({ event }: GeneralEventDetailProps) {
    const [form] = Form.useForm<CreateEventFormBody>();
    const queryClient = useQueryClient();
    const { message, notification } = App.useApp();

    const { mutate: onEventUpdate, isPending: isEventUpdateLoading } = useMutation({
        mutationFn: (partialEvent: Partial<CreateEventFormBody>) =>
            updateEvent(event.eid, partialEvent),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["event", event.eid] });
            onSuccessMessage("Cambios guardados correctamente", message);
        },

        onError: () => {
            notification.error({
                message: "Error",
                description: "No se pudieron guardar los cambios",
            });
        },
    });

    const handleFormFinish = (values: CreateEventFormBody) => {
        onEventUpdate(values);
    };

    const typeSelectOptions = Object.values(EventType).map((value) => ({
        value,
        label: EventTypeLabels[value],
    }));

    const modalitySelectOptions = Object.values(EventModality).map((value) => ({
        value,
        label: EventModalityLabels[value],
    }));

    return (
        <Form
            name="generalEventForm"
            layout="vertical"
            form={form}
            initialValues={{
                name: event.name,
                description: event.description,
                type: event.type,
                modality: event.modality,
                instructor: event.instructor?.iid,
                offering: event.offering?.oid,
                location: event.location,
                price: event.price,
            }}
            onFinish={handleFormFinish}
        >
            <div className="grid grid-cols-1 lg:grid-cols-6 gap-y-6 lg:gap-6">
                <div className="bg-white-full col-span-4 p-5 rounded-lg shadow-sm">
                    <p className="text-gray-400 font-semibold text-sm">
                        INFORMACIÓN GENERAL
                    </p>
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-x-4">
                        <Form.Item
                            name="name"
                            label={<FormLabel>Nombre del evento</FormLabel>}
                            rules={[
                                {
                                    required: true,
                                    message: "Por favor, ingrese el nombre del evento.",
                                },
                            ]}
                        >
                            <Input placeholder="Ej. ¿Cómo ingresar a los CEUs?" />
                        </Form.Item>

                        <Form.Item
                            name="type"
                            label={<FormLabel>Tipo</FormLabel>}
                            rules={[
                                {
                                    required: true,
                                    message: "Por favor, seleccione el tipo de evento.",
                                },
                            ]}
                        >
                            <Select options={typeSelectOptions} />
                        </Form.Item>

                        <Form.Item
                            name="modality"
                            label={<FormLabel>Modalidad</FormLabel>}
                            rules={[
                                {
                                    required: true,
                                    message: "Por favor, seleccione la modalidad.",
                                },
                            ]}
                        >
                            <Select options={modalitySelectOptions} />
                        </Form.Item>

                        <Form.Item
                            name="location"
                            label={<FormLabel>Ubicación</FormLabel>}
                        >
                            <Input placeholder="Ej. Zoom, Google Meet, etc." />
                        </Form.Item>

                        <Form.Item
                            name="description"
                            label={<FormLabel>Descripción</FormLabel>}
                            className="col-span-2"
                        >
                            <TextArea
                                rows={4}
                                placeholder="Descripción del evento..."
                            />
                        </Form.Item>
                    </div>

                    <p className="text-gray-400 font-semibold text-sm mt-6">
                        PERSONAL ASIGNADO
                    </p>
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-x-4">
                        <Form.Item
                            name="instructor"
                            label={<FormLabel>Instructor</FormLabel>}
                            rules={[
                                {
                                    required: true,
                                    message: "Por favor, seleccione un instructor.",
                                },
                            ]}
                        >
                            <SelectInstructor />
                        </Form.Item>

                        <Form.Item
                            name="offering"
                            label={<FormLabel>Oferta</FormLabel>}
                            rules={[
                                {
                                    required: true,
                                    message: "Por favor, seleccione una oferta.",
                                },
                            ]}
                        >
                            <SelectOfferings />
                        </Form.Item>
                    </div>
                </div>

                <div className="col-span-2 space-y-2">
                    <div className="bg-white-full p-5 rounded-lg shadow-sm space-y-2">
                        <p className="text-gray-400 font-semibold text-sm">ACCIONES</p>
                        <div className="flex justify-end gap-3">
                            <Button
                                type="primary"
                                size="large"
                                style={{ fontSize: 16 }}
                                icon={<Trash />}
                                danger
                                block
                            >
                                Eliminar
                            </Button>
                            <Button
                                block
                                type="primary"
                                size="large"
                                style={{ fontSize: 16 }}
                                icon={<Save />}
                                disabled={isEventUpdateLoading}
                                onClick={() => form.submit()}
                            >
                                Guardar
                            </Button>
                        </div>
                    </div>

                    <div className="bg-white-full p-5 rounded-lg shadow-sm space-y-2">
                        <p className="text-gray-400 font-semibold text-sm">
                            CONTENIDO MULTIMEDIA
                        </p>
                        <div>
                            <FormLabel>Miniatura</FormLabel>
                            <UploadEventThumbnail
                                eid={event.eid}
                                initialEventThumbnail={event.thumbnail}
                            />
                        </div>

                        <div>
                            <FormLabel>Portada</FormLabel>
                            <UploadEventCover
                                eid={event.eid}
                                initialEventCoverImage={event.coverImage}
                            />
                        </div>
                    </div>
                </div>
            </div>
        </Form>
    );
}
