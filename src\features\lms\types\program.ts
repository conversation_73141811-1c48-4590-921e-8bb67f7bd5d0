import type {
    Module,
    OfferingFormat,
    OfferingModality,
    OfferingStage,
    OfferingThumbnail,
    OfferingType,
} from "@myTypes/offering";
import type { EnrollmentUser } from "./enrollment";
import type { Dayjs } from "dayjs";

export type ProgramQueryParams = {
    page?: number;
    limit?: number;
    search?: string;
    stage?: OfferingStage;
    modality?: OfferingModality;
    type?: OfferingType;
    format?: OfferingFormat;
    startDate?: string;
    endDate?: string;
};

// Team channels (For chat groups associated to the program)
export type TeamChannel = {
    id: string;
    subject: string;
    subjectTime: number;
    description?: string;
    pictureUrl?: string;
    size: number; // Number of members
    creation: number;
};

export type Program = {
    oid: string;
    name: string;
    longName?: string;
    codeName?: string;
    startDate: string;
    endDate: string;
    description?: string;
    duration?: string;
    frequency?: string;
    hours?: number;
    schedule?: string;
    modality: OfferingModality;
    type: OfferingType;
    stage: OfferingStage;
    format: OfferingFormat;
    thumbnail?: OfferingThumbnail | null;
    extReference: string | null;
    teamChannelId: string | null;

    createdAt: string | Date;
    updatedAt: string | Date;
};

export type RetrieveProgram = {
    // instructors: OfferingInstructor[] | string[]; TODO: change by teachers
    // objectives: OfferingObjective[];
    modules: Module[];
    teamChannelId: string | null;
} & Program;

export type PartialUpdateProgramBody = Partial<
    Omit<Program, "oid" | "createdAt" | "updatedAt" | "thumbnail">
> & {
    codeName?: string;
    rangeDate?: [Dayjs, Dayjs];
};

type ProgramEnrollmentOrderItem = {
    id: number;
    order: string;
    extInvitationStatus: string;
};

export type ProgramEnrollment = {
    key: string;
    user: EnrollmentUser;
    fullName?: string;
    email?: string;
    acceptedInvitation: boolean | null;
    needsConciliation: boolean;
    orderItem: ProgramEnrollmentOrderItem;
};

export type ProgramEnrollmentsResponse = {
    enrollmentsCount: number;
    acceptedInvitationCount: number;
    needsConciliationCount: number;
    enrollments: ProgramEnrollment[];
};
