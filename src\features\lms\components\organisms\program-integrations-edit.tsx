import { useState } from "react";
import {
    <PERSON><PERSON>,
    <PERSON>,
    Typo<PERSON>,
    Switch,
    Input,
    Form,
    Row,
    Col,
    Tag,
    Tooltip,
} from "antd";
import { Globe, Save, MessageCircle, InfoIcon } from "lucide-react";

import { Program } from "@/features/lms/types/program";

const { Title, Text } = Typography;

interface IntegrationsProgramEditProps {
    data: Program;
}

interface IntegrationSettings {
    googleClassroom: {
        enabled: boolean;
        classroomId?: string;
    };
    whatsapp: {
        enabled: boolean;
        groupId?: string;
    };
}

export default function IntegrationsProgramEdit({
    data,
}: IntegrationsProgramEditProps) {
    const [form] = Form.useForm();
    const [integrations, setIntegrations] = useState<IntegrationSettings>({
        googleClassroom: {
            enabled: data.extReference !== null,
        },
        whatsapp: {
            enabled: data.extReference !== null,
        },
    });

    const handleIntegrationToggle = (
        integration: keyof IntegrationSettings,
        enabled: boolean,
    ) => {
        setIntegrations((prev) => ({
            ...prev,
            [integration]: {
                ...prev[integration],
                enabled,
            },
        }));
    };

    const handleSaveIntegrations = () => {
        // TODO: Implement save functionality
        console.log("Saving integrations:", integrations);
    };

    const IntegrationCard = ({
        title,
        description,
        icon: Icon,
        enabled,
        onToggle,
        children,
        status = "available",
    }: {
        title: string;
        description: string;
        icon: React.ElementType;
        enabled: boolean;
        onToggle: (enabled: boolean) => void;
        children?: React.ReactNode;
        status?: "available" | "coming-soon" | "beta";
    }) => (
        <Card className="shadow-sm mb-4">
            <div className="flex items-start justify-between mb-4">
                <div className="flex items-start gap-3">
                    <div className="p-2 bg-blue-50 rounded-lg">
                        <Icon size={20} className="text-blue-600" />
                    </div>
                    <div>
                        <div className="flex items-center gap-2">
                            <Title level={5} className="mb-1">
                                {title}
                            </Title>
                            {status === "beta" && <Tag color="orange">Beta</Tag>}
                            {status === "coming-soon" && (
                                <Tag color="gray">Próximamente</Tag>
                            )}
                        </div>
                        <Text type="secondary" className="text-sm">
                            {description}
                        </Text>
                    </div>
                </div>
                <Switch
                    checked={enabled}
                    onChange={onToggle}
                    disabled={status === "coming-soon"}
                />
            </div>
            {enabled && children && <div className="pl-11">{children}</div>}
        </Card>
    );

    return (
        <div className="space-y-6">
            <Card className="shadow-sm">
                <div className="flex justify-between items-center mb-6">
                    <div>
                        <Title level={4}>Integraciones</Title>
                        <Text type="secondary">
                            Conecta el programa con plataformas externas y herramientas
                            de aprendizaje
                        </Text>
                    </div>
                    <Button
                        type="primary"
                        icon={<Save size={16} />}
                        onClick={handleSaveIntegrations}
                        className="bg-blue-full  hover:bg-green-600"
                    >
                        Guardar Integraciones
                    </Button>
                </div>

                <div className="space-y-4">
                    <IntegrationCard
                        title="Google Classroom"
                        description="Sincroniza estudiantes con Google Classroom"
                        icon={Globe}
                        enabled={integrations.googleClassroom.enabled}
                        onToggle={(enabled) =>
                            handleIntegrationToggle("googleClassroom", enabled)
                        }
                    >
                        <Row gutter={16}>
                            <Col xs={24} md={12}>
                                <Form.Item label="Código de Clase">
                                    <Input placeholder="classroom-id-123" />
                                </Form.Item>
                            </Col>
                        </Row>
                        <Tooltip
                            title="Cómo obtener el ID de Classroom"
                            className="flex items-center gap-1 text-gray-500 w-fit"
                        >
                            <InfoIcon size={16} />
                            Cómo obtener el código de clase
                        </Tooltip>
                    </IntegrationCard>

                    <IntegrationCard
                        title="WhatsApp"
                        description="Invita automáticamente a los inscritos al grupo de WhatsApp del programa"
                        icon={MessageCircle}
                        enabled={integrations.whatsapp.enabled}
                        onToggle={(enabled) =>
                            handleIntegrationToggle("whatsapp", enabled)
                        }
                        status="available"
                    >
                        <Row gutter={16}>
                            <Col xs={24} md={12}>
                                <Form.Item label="Grupo">
                                    <Input placeholder="CEU-PEFA-2025-II" />
                                </Form.Item>
                            </Col>
                        </Row>
                    </IntegrationCard>
                </div>
            </Card>
        </div>
    );
}
