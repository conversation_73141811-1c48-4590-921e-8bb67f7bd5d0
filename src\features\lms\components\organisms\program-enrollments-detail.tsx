import { <PERSON><PERSON>, Button, Col, Dropdown, <PERSON>, <PERSON>uProps, Row, Typography } from "antd";
import { useProgramEnrollments } from "../../hooks/use-program";
import StatCard from "@/features/crm/components/atoms/stat-card";
import { MoreVerticalIcon, Users2 } from "lucide-react";
import { ProgramEnrollment } from "../../types/program";
import { useNavigate } from "react-router-dom";

const { Title } = Typography;

interface ProgramEnrollmentsDetailProps {
    oid: string;
}

export default function ProgramEnrollmentsDetail({
    oid,
}: ProgramEnrollmentsDetailProps) {
    const navigate = useNavigate();
    const { data, isLoading, isError } = useProgramEnrollments(oid);

    const DROPDOWN_ITEMS: MenuProps["items"] = [
        {
            key: "viewEnrollment",
            label: (
                <div className="flex items-center gap-2 text-blue-full">
                    Ver matrícula
                </div>
            ),
        },
        {
            key: "viewOrder",
            label: (
                <div className="flex items-center gap-2 text-blue-full">Ver orden</div>
            ),
        },
        {
            key: "delete",
            label: (
                <div className="flex items-center gap-2 text-state-red-full">
                    Eliminar de la clase
                </div>
            ),
        },
    ];

    const handleRowAction = (key: string, record: ProgramEnrollment) => {
        if (key === "viewEnrollment") {
            navigate(`/lms/enrollments/${record.key}`);
        }

        if (key === "viewOrder") {
            if (!record.orderItem?.order) return;
            navigate(`/crm/orders/${record.orderItem.order}`);
        }
        if (key === "delete") {
            // TODO: Implement delete functionality
        }
    };

    return (
        <section>
            {/* Responsive stat cards */}
            <Row gutter={[16, 16]}>
                <Col xs={12} md={8}>
                    <StatCard
                        title="Total de inscripciones"
                        value={data?.enrollmentsCount ?? 0}
                        icon={<Users2 size={24} className="text-blue-500" />}
                        color="#4096ff"
                    />
                </Col>
                <Col xs={12} md={8}>
                    {data?.acceptedInvitationCount}
                </Col>
                <Col xs={12} md={8}>
                    {data?.needsConciliationCount}
                </Col>
            </Row>
            <Row gutter={[16, 16]}>
                <Col xs={24} className="mt-8">
                    <div className="p-4">
                        <Title level={2} color="gray">
                            Estudiantes
                        </Title>
                        {data && data?.enrollments.length ? (
                            <List
                                itemLayout="horizontal"
                                dataSource={data.enrollments}
                                renderItem={(item) => (
                                    <List.Item>
                                        <List.Item.Meta
                                            avatar={
                                                <Avatar>
                                                    {item.user.fullName?.[0] ??
                                                        item.fullName?.[0] ??
                                                        "S"}
                                                    {item.needsConciliation}
                                                    {item.acceptedInvitation}
                                                    {item.orderItem.extInvitationStatus}
                                                </Avatar>
                                            }
                                            title={
                                                item.user.fullName ||
                                                item.fullName ||
                                                "Sin nombre"
                                            }
                                            description={
                                                <div>
                                                    {item.user.email ??
                                                        item.user.email ??
                                                        ""}
                                                </div>
                                            }
                                        />
                                        <div>
                                            <Dropdown
                                                trigger={["click"]}
                                                menu={{
                                                    items: DROPDOWN_ITEMS,
                                                    onClick: ({ key }) => {
                                                        handleRowAction(key, item);
                                                    },
                                                }}
                                            >
                                                <Button
                                                    icon={<MoreVerticalIcon />}
                                                    type="text"
                                                    size="small"
                                                />
                                            </Dropdown>
                                        </div>
                                    </List.Item>
                                )}
                            />
                        ) : (
                            <div className="flex justify-center items-center">
                                <div className="text-gray-400 text-center">
                                    No hay inscripciones para este programa
                                </div>
                            </div>
                        )}
                    </div>
                </Col>
            </Row>
        </section>
    );
}
