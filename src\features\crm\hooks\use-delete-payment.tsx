import { useMutation, useQueryClient } from "@tanstack/react-query";
import { App, Modal } from "antd";
import { deletePayment } from "../services/portals/payment";
import { AlertTriangle } from "lucide-react";
import type { AxiosError } from "axios";
import { useApiError } from "@hooks/use-api-error";

export const useDeletePayment = () => {
    const queryClient = useQueryClient();
    const { notification } = App.useApp();
    const { confirm } = Modal;

    const { handleError } = useApiError({
        title: "Error al eliminar el pago",
        genericMessage: "No se pudo eliminar el pago. Inténtalo de nuevo",
    });

    const deleteMutation = useMutation({
        mutationFn: (pid: string) => deletePayment(pid),
        onSuccess: () => {
            notification.success({
                message: "Pago eliminado",
                description: "El pago se ha eliminado correctamente",
                duration: 3,
            });
            queryClient.invalidateQueries({ queryKey: ["payments"] });
        },
        onError: (error: AxiosError) => {
            handleError(error);
        },
    });

    const confirmDelete = (pid: string) => {
        confirm({
            title: "¿Estás seguro de eliminar este pago?",
            icon: <AlertTriangle className="text-orange-500" />,
            content:
                "Esta acción no se puede deshacer. El pago será eliminado permanentemente.",
            okText: "Sí, eliminar",
            okType: "danger",
            cancelText: "Cancelar",
            onOk() {
                deleteMutation.mutate(pid);
            },
        });
    };

    return {
        deletePayment: confirmDelete,
        isLoading: deleteMutation.isPending,
    };
};
