import { PaginatedResponse } from "@myTypes/base";
import { portalsApi } from "@services/portals";
import type {
    PartialUpdateProgramBody,
    Program,
    ProgramEnrollmentsResponse,
    ProgramQueryParams,
    RetrieveProgram,
    TeamChannel,
} from "../../types/program";

export const getPrograms = async (
    params: ProgramQueryParams = {},
): Promise<PaginatedResponse<Program>> => {
    const response = await portalsApi.get("lms/programs", {
        params,
    });
    return response.data;
};

export const retrieveProgram = async (oid: string): Promise<RetrieveProgram> => {
    const response = await portalsApi.get(`lms/programs/${oid}`);
    return response.data;
};

export const partialUpdateProgram = async (
    oid: string,
    data: PartialUpdateProgramBody,
): Promise<RetrieveProgram> => {
    const response = await portalsApi.patch(`lms/programs/${oid}`, data);
    return response.data;
};

export const listProgramEnrollments = async (
    oid: string,
): Promise<ProgramEnrollmentsResponse> => {
    const response = await portalsApi.get(`lms/programs/${oid}/enrollments`);
    return response.data;
};

export const retrieveProgramTeamChannel = async (oid: string): Promise<TeamChannel> => {
    const response = await portalsApi.get(`lms/programs/${oid}/team-channel`);
    return response.data;
};
